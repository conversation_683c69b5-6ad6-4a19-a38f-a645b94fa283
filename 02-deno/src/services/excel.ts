import ExcelJS from 'exceljs';
import { join } from '@std/path/mod.ts';
import type {
  CombinedResult,
  ExcelExportOptions
} from '../types.ts';
import { logger } from '../utils/logger.ts';
import { FileUtils } from '../utils/file.ts';

export class ExcelService {
  private outputDir: string;

  constructor(outputDir: string) {
    this.outputDir = outputDir;
  }

  async init(): Promise<void> {
    await FileUtils.ensureDirectoryExists(this.outputDir);
  }

  async exportToExcel(
    combinedResult: CombinedResult,
    options: ExcelExportOptions
  ): Promise<string> {
    try {
      await logger.info('Creating Excel file with business listings');

      const workbook = new ExcelJS.Workbook();

      // Set workbook properties
      workbook.creator = 'Guide OCR Deno';
      workbook.lastModifiedBy = 'Guide OCR Deno';
      workbook.created = new Date();
      workbook.modified = new Date();

      // Create main data sheet
      await this.createDataSheet(workbook, combinedResult, options);

      // Create metadata sheet if requested
      if (options.includeMetadata) {
        await this.createMetadataSheet(workbook, combinedResult);
      }

      // Create statistics sheet if requested
      if (options.includeStatistics) {
        await this.createStatisticsSheet(workbook, combinedResult);
      }

      // Save the file
      const filePath = join(this.outputDir, options.filename);
      await workbook.xlsx.writeFile(filePath);

      await logger.success(`Excel file created: ${filePath}`);
      return filePath;
    } catch (error) {
      await logger.error('Failed to create Excel file', error);
      throw error;
    }
  }

  private async createDataSheet(
    workbook: ExcelJS.Workbook,
    combinedResult: CombinedResult,
    options: ExcelExportOptions
  ): Promise<void> {
    const worksheet = workbook.addWorksheet(options.sheetName);

    // Create headers
    const headers = [
      'Entry ID',
      'Page Number',
      'Confidence Score',
      ...combinedResult.columns.map(col => col.name),
      'Spans Multiple Pages',
      'Continued From Page',
      'Continues On Page'
    ];

    // Add headers
    const headerRow = worksheet.addRow(headers);

    // Style headers
    headerRow.eachCell((cell) => {
      cell.font = { bold: true, color: { argb: 'FFFFFF' } };
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '366092' }
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' }
      };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
    });

    // Add data rows
    combinedResult.listings.forEach((listing, index) => {
      const rowData = [
        index + 1, // Entry ID (sequential row number for display)
        listing.pageNumber,
        listing.confidence,
        ...combinedResult.columns.map(col => listing.data[col.name] || ''),
        listing.spanMultiplePages ? 'Yes' : 'No',
        listing.continuedFromPage || '',
        listing.continuesOnPage || ''
      ];

      const dataRow = worksheet.addRow(rowData);

      // Style data rows
      dataRow.eachCell((cell, colNumber) => {
        cell.border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' }
        };

        // Color code confidence scores
        if (colNumber === 3) { // Confidence column
          const confidence = listing.confidence;
          if (confidence >= 0.9) {
            cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'C6EFCE' } };
          } else if (confidence >= 0.7) {
            cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFEB9C' } };
          } else {
            cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFC7CE' } };
          }
        }

        // Highlight entries spanning multiple pages
        if (listing.spanMultiplePages) {
          cell.font = { italic: true };
        }
      });
    });

    // Auto-fit columns if requested
    if (options.autoFitColumns) {
      worksheet.columns.forEach((column) => {
        if (column.eachCell) {
          let maxLength = 0;
          column.eachCell({ includeEmpty: true }, (cell) => {
            const columnLength = cell.value ? cell.value.toString().length : 10;
            if (columnLength > maxLength) {
              maxLength = columnLength;
            }
          });
          column.width = Math.min(maxLength + 2, 50); // Cap at 50 characters
        }
      });
    }

    // Freeze the header row
    worksheet.views = [{ state: 'frozen', ySplit: 1 }];

    await logger.debug(`Data sheet created with ${combinedResult.listings.length} rows`);
  }

  private async createMetadataSheet(
    workbook: ExcelJS.Workbook,
    combinedResult: CombinedResult
  ): Promise<void> {
    const worksheet = workbook.addWorksheet('Metadata');

    // Add metadata information
    const metadata = [
      ['Export Timestamp', combinedResult.timestamp],
      ['Total Listings', combinedResult.totalListings],
      ['Total Pages Processed', combinedResult.totalPages],
      ['Average Entries Per Page', combinedResult.statistics.averageEntriesPerPage.toFixed(2)],
      ['Pages with Multiple Entries', combinedResult.statistics.pagesWithMultipleEntries],
      ['Entries Spanning Pages', combinedResult.statistics.entriesSpanningPages],
      ['Completeness Score', `${(combinedResult.statistics.completenessScore * 100).toFixed(1)}%`],
      [''],
      ['Column Definitions', ''],
    ];

    // Add column definitions
    combinedResult.columns.forEach(col => {
      metadata.push([
        `${col.name} (${col.dataType})`,
        `${col.description} | Required: ${col.required ? 'Yes' : 'No'}`
      ]);
    });

    // Add all metadata rows
    metadata.forEach(([key, value]) => {
      const row = worksheet.addRow([key, value]);
      if (key === 'Column Definitions' || key === '') {
        row.getCell(1).font = { bold: true };
      }
    });

    // Style the metadata sheet
    worksheet.getColumn(1).width = 30;
    worksheet.getColumn(2).width = 50;

    await logger.debug('Metadata sheet created');
  }

  private async createStatisticsSheet(
    workbook: ExcelJS.Workbook,
    combinedResult: CombinedResult
  ): Promise<void> {
    const worksheet = workbook.addWorksheet('Statistics');

    // Calculate additional statistics
    const stats = this.calculateDetailedStatistics(combinedResult);

    // Add statistics
    const statisticsData = [
      ['Statistic', 'Value'],
      ['Total Business Listings', combinedResult.totalListings],
      ['Total Pages Processed', combinedResult.totalPages],
      ['Average Entries Per Page', stats.averageEntriesPerPage.toFixed(2)],
      ['Pages with Multiple Entries', stats.pagesWithMultipleEntries],
      ['Entries Spanning Multiple Pages', stats.entriesSpanningPages],
      ['Overall Completeness Score', `${(stats.completenessScore * 100).toFixed(1)}%`],
      [''],
      ['Confidence Distribution', ''],
      ['High Confidence (≥90%)', stats.confidenceDistribution.high],
      ['Medium Confidence (70-89%)', stats.confidenceDistribution.medium],
      ['Low Confidence (<70%)', stats.confidenceDistribution.low],
      [''],
      ['Column Completeness', ''],
    ];

    // Add column completeness statistics
    stats.columnCompleteness.forEach(([column, percentage]) => {
      statisticsData.push([column, `${percentage.toFixed(1)}%`]);
    });

    // Add all statistics rows
    statisticsData.forEach(([key, value], index) => {
      const row = worksheet.addRow([key, value]);

      if (index === 0) { // Header row
        row.eachCell(cell => {
          cell.font = { bold: true };
          cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: '366092' } };
          cell.font = { bold: true, color: { argb: 'FFFFFF' } };
        });
      } else if (key === 'Confidence Distribution' || key === 'Column Completeness' || key === '') {
        row.getCell(1).font = { bold: true };
      }
    });

    // Style the statistics sheet
    worksheet.getColumn(1).width = 30;
    worksheet.getColumn(2).width = 20;

    await logger.debug('Statistics sheet created');
  }

  private calculateDetailedStatistics(combinedResult: CombinedResult) {
    const listings = combinedResult.listings;

    // Confidence distribution
    const confidenceDistribution = {
      high: listings.filter(l => l.confidence >= 0.9).length,
      medium: listings.filter(l => l.confidence >= 0.7 && l.confidence < 0.9).length,
      low: listings.filter(l => l.confidence < 0.7).length,
    };

    // Column completeness
    const columnCompleteness = combinedResult.columns.map(col => {
      const filledCount = listings.filter(listing =>
        listing.data[col.name] !== null &&
        listing.data[col.name] !== undefined &&
        listing.data[col.name] !== ''
      ).length;
      const percentage = (filledCount / listings.length) * 100;
      return [col.name, percentage] as [string, number];
    });

    return {
      ...combinedResult.statistics,
      confidenceDistribution,
      columnCompleteness,
    };
  }
}
