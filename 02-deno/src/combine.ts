import { join } from '@std/path/mod.ts';
import { Config } from './config.ts';
import { ExcelService } from './services/excel.ts';
import { ExtractUtility } from './extract.ts';
import { AnalyzeUtility } from './analyze.ts';
import { logger } from './utils/logger.ts';
import { FileUtils } from './utils/file.ts';
import type {
  CombinedResult,
  ExtractionResult,
  BusinessListing,
  ExcelExportOptions,
  CategoryMapping
} from './types.ts';

export class CombineUtility {
  private config: Config;
  private excelService: ExcelService;
  private extractUtility: ExtractUtility;
  private analyzeUtility: AnalyzeUtility;

  constructor() {
    this.config = {} as Config;
    this.excelService = {} as ExcelService;
    this.extractUtility = new ExtractUtility();
    this.analyzeUtility = new AnalyzeUtility();
  }

  async init(): Promise<void> {
    await logger.init();
    await logger.info('Initializing Combine Utility');

    // Load configuration
    this.config = await Config.getInstance();
    await this.config.validateConfig();

    const processingConfig = this.config.getProcessingConfig();

    // Initialize services
    this.excelService = new ExcelService(processingConfig.outputDir);
    await this.excelService.init();
    await this.extractUtility.init();
    await this.analyzeUtility.init();

    await logger.success('Combine Utility initialized successfully');
  }

  async combine(): Promise<CombinedResult> {
    try {
      await logger.info('Starting data combination process');

      // Load analysis result
      const analysis = await this.analyzeUtility.getLatestAnalysis();
      if (!analysis) {
        throw new Error('No analysis result found. Please run analysis first.');
      }

      // Load all extraction results
      const extractionResults = await this.extractUtility.getAllExtractionResults();
      if (extractionResults.length === 0) {
        throw new Error('No extraction results found. Please run extraction first.');
      }

      await logger.info(`Combining data from ${extractionResults.length} pages`);

      // Combine all listings
      const allListings = this.combineListings(extractionResults);

      // Handle multi-page entries
      const processedListings = this.mergeMultiPageEntries(allListings);

      // Apply categories to listings
      const listingsWithCategories = await this.applyCategoriesFromMapping(processedListings);

      // Calculate statistics
      const statistics = this.calculateStatistics(listingsWithCategories, extractionResults);

      // Create combined result
      const combinedResult: CombinedResult = {
        timestamp: new Date().toISOString(),
        totalListings: listingsWithCategories.length,
        totalPages: extractionResults.length,
        columns: analysis.columns,
        listings: listingsWithCategories,
        statistics,
      };

      // Save combined result
      await this.saveCombinedResult(combinedResult);

      await logger.success(`Data combination completed. Combined ${listingsWithCategories.length} listings from ${extractionResults.length} pages`);

      return combinedResult;

    } catch (error) {
      await logger.error('Data combination failed', error);
      throw error;
    }
  }

  private combineListings(extractionResults: ExtractionResult[]): BusinessListing[] {
    const allListings: BusinessListing[] = [];
    let globalEntryNumber = 1;

    for (const result of extractionResults) {
      for (const listing of result.listings) {
        // Assign global entry number
        listing.entryNumber = globalEntryNumber++;
        allListings.push(listing);
      }
    }

    return allListings;
  }

  private mergeMultiPageEntries(listings: BusinessListing[]): BusinessListing[] {
    // Step 1: Handle continuation entries first
    const { processedListings, continuationIndices } = this.handleContinuationEntries(listings);

    // Step 2: Process remaining entries (non-continuation entries)
    const finalListings = this.processRemainingEntries(listings, processedListings, continuationIndices);

    return finalListings;
  }



  private handleContinuationEntries(listings: BusinessListing[]): {
    processedListings: BusinessListing[],
    continuationIndices: Set<number>
  } {
    const processedListings: BusinessListing[] = [];
    const continuationIndices = new Set<number>();
    const mergedPreviousIndices = new Set<number>();

    // Find all continuation entries and merge them with their previous page entries
    for (let i = 0; i < listings.length; i++) {
      const listing = listings[i];

      if (listing?.isContinuationFromPreviousPage) {
        continuationIndices.add(i); // Always mark continuation entries as processed

        // Find the previous page's last entry that continues to this page
        const previousPageNumber = listing.pageNumber - 1;
        let previousPageLastEntry: BusinessListing | null = null;
        let previousEntryIndex = -1;

        // Look backwards to find the last entry of the previous page that continues
        for (let j = i - 1; j >= 0; j--) {
          const prevListing = listings[j];
          if (prevListing && prevListing.pageNumber === previousPageNumber) {
            // Check if this entry continues to the next page
            if (prevListing.continuesOnNextPage || prevListing.spanMultiplePages ||
              prevListing.continuesOnPage === listing.pageNumber) {
              if (!previousPageLastEntry || prevListing.entryNumber > previousPageLastEntry.entryNumber) {
                previousPageLastEntry = prevListing;
                previousEntryIndex = j;
              }
            }
          }
        }

        if (previousPageLastEntry && previousEntryIndex !== -1) {
          // Mark the previous entry as merged so we don't process it again
          mergedPreviousIndices.add(previousEntryIndex);

          // Create merged entry
          const mergedListing = this.createMergedListing(previousPageLastEntry, listing);
          processedListings.push(mergedListing);
        } else {
          // No previous entry found to merge with
          // This can happen if the previous page entry doesn't have the correct continuation flags
          // Skip this continuation entry as it cannot be properly merged
        }
      }
    }

    return { processedListings, continuationIndices: new Set([...continuationIndices, ...mergedPreviousIndices]) };
  }

  private createMergedListing(previousListing: BusinessListing, continuationListing: BusinessListing): BusinessListing {
    // Start with the previous listing as the base
    const mergedListing: BusinessListing = {
      ...previousListing,
      data: { ...previousListing.data }
    };

    // Merge data fields from continuation, only filling in missing fields
    for (const [key, value] of Object.entries(continuationListing.data)) {
      if (value && value !== '' && value !== null &&
        (!mergedListing.data[key] || mergedListing.data[key] === '' || mergedListing.data[key] === null)) {
        mergedListing.data[key] = value;
      }
    }

    // Concatenate raw text from both entries
    const rawTexts: string[] = [];

    // Check if raw text is in the data object instead of as a direct property
    const prevRawText = previousListing.rawText || previousListing.data.Raw_Text as string;
    const contRawText = continuationListing.rawText || continuationListing.data.Raw_Text as string;

    if (prevRawText) {
      rawTexts.push(prevRawText);
    }
    if (contRawText) {
      rawTexts.push(contRawText);
    }
    if (rawTexts.length > 0) {
      mergedListing.rawText = rawTexts.join(' ');
    }

    // Update confidence to average
    mergedListing.confidence = (previousListing.confidence + continuationListing.confidence) / 2;

    // Mark as spanning multiple pages
    mergedListing.spanMultiplePages = true;
    mergedListing.continuesOnPage = continuationListing.pageNumber;
    mergedListing.continuedFromPage = previousListing.pageNumber;

    return mergedListing;
  }

  private processRemainingEntries(
    allListings: BusinessListing[],
    continuationMergedListings: BusinessListing[],
    skipIndices: Set<number>
  ): BusinessListing[] {
    const finalListings: BusinessListing[] = [...continuationMergedListings];

    // Process all entries that weren't involved in continuation merging
    for (let i = 0; i < allListings.length; i++) {
      if (skipIndices.has(i)) continue;

      const listing = allListings[i];
      if (!listing) continue;

      // Check if this entry spans multiple pages (but is not a continuation)
      if ((listing.spanMultiplePages || listing.continuesOnNextPage) && !listing.isContinuationFromPreviousPage) {
        // Look for its continuation on the next page
        const nextPageContinuation = this.findNextPageContinuation(allListings, listing, i);
        if (nextPageContinuation) {
          const mergedListing = this.createMergedListing(listing, nextPageContinuation.listing);
          finalListings.push(mergedListing);
          skipIndices.add(nextPageContinuation.index);
        } else {
          // No continuation found, add as is
          finalListings.push(listing);
        }
      } else {
        // Regular entry, add as is
        finalListings.push(listing);
      }
    }

    return finalListings;
  }

  private findNextPageContinuation(
    listings: BusinessListing[],
    baseListing: BusinessListing,
    startIndex: number
  ): { listing: BusinessListing, index: number } | null {
    const nextPageNumber = baseListing.pageNumber + 1;

    // Look forward for a continuation entry on the next page
    for (let i = startIndex + 1; i < listings.length; i++) {
      const listing = listings[i];
      if (listing && listing.pageNumber === nextPageNumber && listing.isContinuationFromPreviousPage) {
        return { listing, index: i };
      }
      // Stop looking if we've gone past the next page
      if (listing && listing.pageNumber > nextPageNumber) {
        break;
      }
    }

    return null;
  }

  private calculateStatistics(listings: BusinessListing[], extractionResults: ExtractionResult[]) {
    const totalPages = extractionResults.length;
    const totalListings = listings.length;

    const averageEntriesPerPage = totalListings / totalPages;

    const pagesWithMultipleEntries = extractionResults.filter(
      result => result.totalEntries > 1
    ).length;

    const entriesSpanningPages = listings.filter(
      listing => listing.spanMultiplePages
    ).length;

    // Calculate completeness score based on how many fields are filled
    const totalFields = listings.length * Object.keys(listings[0]?.data || {}).length;
    const filledFields = listings.reduce((count, listing) => {
      return count + Object.values(listing.data).filter(value =>
        value !== null && value !== undefined && value !== ''
      ).length;
    }, 0);

    const completenessScore = totalFields > 0 ? filledFields / totalFields : 0;

    return {
      averageEntriesPerPage,
      pagesWithMultipleEntries,
      entriesSpanningPages,
      completenessScore,
    };
  }

  private async saveCombinedResult(result: CombinedResult): Promise<void> {
    const processingConfig = this.config.getProcessingConfig();
    const combinedDir = join(processingConfig.outputDir, 'combined');
    await FileUtils.ensureDirectoryExists(combinedDir);

    const filename = FileUtils.generateTimestampedFilename('combined_data', 'json');
    const filePath = join(combinedDir, filename);

    await FileUtils.writeJsonFile(filePath, result);
    await logger.info(`Combined result saved to: ${filePath}`);

    // Also save as latest.json
    const latestPath = join(combinedDir, 'latest.json');
    await FileUtils.writeJsonFile(latestPath, result);
  }

  private async applyCategoriesFromMapping(listings: BusinessListing[]): Promise<BusinessListing[]> {
    try {
      // Load category mapping
      const categoryMapping = await this.loadCategoryMapping();

      if (Object.keys(categoryMapping).length === 0) {
        await logger.info('No category mapping found, listings will not have categories assigned');
        return listings;
      }

      // Apply categories to listings based on their page numbers
      const listingsWithCategories = listings.map(listing => {
        const category = this.findCategoryForPage(listing.pageNumber, categoryMapping);
        if (category) {
          return {
            ...listing,
            category: category.categoryName
          };
        }
        return listing;
      });

      const categorizedCount = listingsWithCategories.filter(l => l.category).length;
      await logger.info(`Applied categories to ${categorizedCount} out of ${listings.length} listings`);

      return listingsWithCategories;
    } catch (error) {
      await logger.warn('Failed to apply categories from mapping', error);
      return listings;
    }
  }

  private async loadCategoryMapping(): Promise<CategoryMapping> {
    try {
      const processingConfig = this.config.getProcessingConfig();
      const categoryPath = join(processingConfig.outputDir, 'category.json');

      if (await FileUtils.fileExists(categoryPath)) {
        return await FileUtils.readJsonFile<CategoryMapping>(categoryPath);
      }

      return {};
    } catch (error) {
      await logger.warn('Failed to load category mapping', error);
      return {};
    }
  }

  private findCategoryForPage(pageNumber: number, categoryMapping: CategoryMapping): { categoryName: string } | null {
    // Find the most recent category that applies to this page
    let applicableCategory: { categoryName: string } | null = null;
    let latestCategoryPage = 0;

    for (const [pageStr, categoryInfo] of Object.entries(categoryMapping)) {
      const categoryPage = parseInt(pageStr, 10);

      // Category applies if it's on or before the current page
      if (categoryPage <= pageNumber && categoryPage > latestCategoryPage) {
        applicableCategory = { categoryName: categoryInfo.categoryName };
        latestCategoryPage = categoryPage;
      }
    }

    return applicableCategory;
  }

  async exportToExcel(combinedResult?: CombinedResult): Promise<string> {
    try {
      let result = combinedResult;

      if (!result) {
        // Load latest combined result
        const processingConfig = this.config.getProcessingConfig();
        const latestPath = join(processingConfig.outputDir, 'combined', 'latest.json');

        if (await FileUtils.fileExists(latestPath)) {
          result = await FileUtils.readJsonFile<CombinedResult>(latestPath);
        } else {
          throw new Error('No combined result found. Please run combine first.');
        }
      }

      const excelConfig = this.config.getExcelConfig();
      const options: ExcelExportOptions = {
        filename: excelConfig.filename,
        sheetName: excelConfig.sheetName,
        includeMetadata: true,
        includeStatistics: true,
        autoFitColumns: true,
      };

      const filePath = await this.excelService.exportToExcel(result, options);
      return filePath;

    } catch (error) {
      await logger.error('Excel export failed', error);
      throw error;
    }
  }
}

// CLI function for direct usage
export async function runCombine(): Promise<void> {
  const combiner = new CombineUtility();

  try {
    await combiner.init();
    const result = await combiner.combine();

    console.log('\n📊 Combination Results:');
    console.log(`📄 Pages processed: ${result.totalPages}`);
    console.log(`📋 Total listings: ${result.totalListings}`);
    console.log(`📈 Average entries per page: ${result.statistics.averageEntriesPerPage.toFixed(1)}`);
    console.log(`🔗 Entries spanning pages: ${result.statistics.entriesSpanningPages}`);
    console.log(`✅ Completeness score: ${(result.statistics.completenessScore * 100).toFixed(1)}%`);

    // Export to Excel
    console.log('\n📊 Exporting to Excel...');
    const excelPath = await combiner.exportToExcel(result);
    console.log(`✅ Excel file created: ${excelPath}`);

  } catch (error) {
    console.error('❌ Combination failed:', error);
    Deno.exit(1);
  }
}
